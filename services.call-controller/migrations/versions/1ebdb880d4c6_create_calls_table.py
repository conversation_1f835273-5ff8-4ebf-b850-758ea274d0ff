"""Create calls table

Revision ID: 1ebdb880d4c6
Revises: 
Create Date: 2025-08-22 01:37:09.355189

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1ebdb880d4c6'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('calls',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('status', sa.Enum('QUEUED', 'ACTIVE', 'ENDED', name='callstatus'), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('ended_at', sa.DateTime(), nullable=True),
    sa.Column('media_session_id', sa.String(), nullable=True),
    sa.Column('translation_fork_id', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('media_session_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('calls')
    # ### end Alembic commands ###
