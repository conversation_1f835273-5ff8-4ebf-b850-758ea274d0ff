from fastapi import APIRouter

from .endpoints.calls import router as call
from .endpoints.operators import router as operators
from .endpoints.queue import router as queue

api_router = APIRouter()

api_router.include_router(call, prefix="/calls", tags=["calls"])
api_router.include_router(operators, prefix="/operators", tags=["operators"])
api_router.include_router(queue, prefix="/queue", tags=["queue"])
