import uuid
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from cortexacommon.logging import get_logger

from src.core.context import ApplicationContext, get_app_context
from src.schemas.call import CallResponse

router = APIRouter()
logger = get_logger(__name__)


@router.post("/", response_model=CallResponse, status_code=201)
async def create_call(
    app_context: ApplicationContext = Depends(get_app_context)
):
    """Create a new call."""
    try:
        new_call = await app_context.call_service.create_new_call()
        return new_call
    except Exception as e:
        # The service layer can raise specific exceptions later for more granular error handling
        raise HTTPException(status_code=502, detail=f"Failed to create call: {e}")

@router.post("/{call_id}/end", status_code=204)
async def end_call(
    call_id: uuid.UUID,
    app_context: ApplicationContext = Depends(get_app_context)
):
    """End a call."""
    db_call = await app_context.call_service.end_call(call_id)
    if not db_call:
        raise HTTPException(status_code=404, detail="Call not found")


# @router.post("/{call_id}/features/translation", status_code=201)
# async def enable_translation(
#     call_id: uuid.UUID,
#     app_context: ApplicationContext = Depends(get_app_context)
# ):
#     """Enable translation for a call by starting an RTP fork."""
#     db_call = await app_context.db_session.get(Call, call_id)
#     if not db_call or not db_call.media_session_id:
#         raise HTTPException(status_code=404, detail="Active call session not found")

#     if db_call.translation_fork_id:
#         raise HTTPException(status_code=409, detail="Translation is already enabled")

#     try:
#         fork_id = await app_context.voice_router_service.start_translation_fork(db_call.media_session_id)
#         db_call.translation_fork_id = fork_id
#         await app_context.db_session.commit()
#         return {"fork_id": fork_id}
#     except Exception as e:
#         raise HTTPException(status_code=502, detail=f"Could not enable translation fork: {e}")

# @router.delete("/{call_id}/features/translation", status_code=204)
# async def disable_translation(
#     call_id: uuid.UUID,
#     app_context: ApplicationContext = Depends(get_app_context)
# ):
#     """Disable translation for a call by stopping the RTP fork."""
#     db_call = await app_context.db_session.get(Call, call_id)
#     if not db_call or not db_call.media_session_id or not db_call.translation_fork_id:
#         raise HTTPException(status_code=404, detail="Translation feature not active for this call")

#     try:
#         await app_context.voice_router_service.stop_translation_fork(
#             db_call.media_session_id, db_call.translation_fork_id
#         )
#         db_call.translation_fork_id = None
#         await app_context.db_session.commit()
#     except Exception as e:
#         raise HTTPException(status_code=502, detail=f"Could not disable translation fork: {e}")