import httpx
from ..core.config import settings


class VoiceRouterService:
    """
    A client service for interacting with the internal API of the voice-router.
    """
    def __init__(self):
        self.client = httpx.AsyncClient(base_url=settings.voice_router_url)

    async def create_media_session(self) -> str:
        """
        Commands the voice-router to create a new media session.
        Returns the unique media_session_id.
        """
        response = await self.client.post("/internal/calls")
        response.raise_for_status()  # Raise an exception for non-2xx responses
        return response.json()['mediaSessionId']

    async def end_media_session(self, media_session_id: str):
        """
        Commands the voice-router to terminate a media session.
        """
        response = await self.client.delete(f"/internal/calls/{media_session_id}")
        response.raise_for_status()

    # async def start_translation_fork(self, media_session_id: str) -> str:
    #     """
    #     Commands the voice-router to start an RTP fork for translation.
    #     Returns the unique fork_id.
    #     """
    #     # In a real system, the translation service endpoint would come from config
    #     translation_service_endpoint = {"ip": "127.0.0.1", "port": 5004}
        
    #     response = await self.client.post(
    #         f"/internal/calls/{media_session_id}/forks",
    #         json=translation_service_endpoint
    #     )
    #     response.raise_for_status()
    #     return response.json()["fork_id"]

    # async def stop_translation_fork(self, media_session_id: str, fork_id: str):
    #     """
    #     Commands the voice-router to stop a specific RTP fork.
    #     """
    #     response = await self.client.delete(
    #         f"/internal/calls/{media_session_id}/forks/{fork_id}"
    #     )
    #     response.raise_for_status()