import uuid
from datetime import datetime, timezone
from .voice_router import VoiceRouterService
from ..repositories.call_repository import CallRepository
from ..models.call import Call, CallStatus


class CallService:
    """
    Contains the business logic for managing calls.
    """
    def __init__(self, call_repo: CallRepository, voice_router: VoiceRouterService):
        self.call_repo = call_repo
        self.voice_router = voice_router

    async def create_new_call(self) -> Call:
        """
        Orchestrates the creation of a new call, including its media session.
        """
        # Command the voice-router to create a media session first
        media_session_id = await self.voice_router.create_media_session()

        # If successful, create the call record in our database
        new_call = Call(media_session_id=media_session_id)
        return await self.call_repo.create(new_call)

    async def end_call(self, call_id: uuid.UUID) -> Call | None:
        """
        Orchestrates ending a call, terminating the media session and updating status.
        """
        db_call = await self.call_repo.get_by_id(call_id)
        if not db_call or db_call.status == CallStatus.ENDED:
            return db_call

        if db_call.media_session_id:
            try:
                await self.voice_router.end_media_session(db_call.media_session_id)
            except Exception as e:
                print(f"Warning: Could not end media session {db_call.media_session_id}: {e}")

        db_call.status = CallStatus.ENDED
        db_call.ended_at = datetime.now(timezone.utc)
        return await self.call_repo.save(db_call)
