import uuid
from datetime import datetime, timezone
from .voice_router import VoiceRouterService
from ..repositories.call_repository import CallRepository
from ..models.call import Call, CallStatus
from ..core.events import EventPublisher
from ..schemas.events import CallStartedEvent, CallEndedEvent


class CallService:
    """
    Contains the business logic for managing calls.
    """
    def __init__(self, call_repo: CallRepository, voice_router: VoiceRouterService, event_publisher: EventPublisher):
        self.call_repo = call_repo
        self.voice_router = voice_router
        self.event_publisher = event_publisher

    async def create_new_call(self) -> Call:
        """
        Orchestrates the creation of a new call, including its media session.
        """
        # Command the voice-router to create a media session first
        media_session_id = await self.voice_router.create_media_session()

        # If successful, create the call record in our database
        new_call = Call(media_session_id=media_session_id)
        created_call = await self.call_repo.create(new_call)

        # Publish call started event
        try:
            event = CallStartedEvent(call_id=str(created_call.id))
            await self.event_publisher.publish_event(event)
        except Exception as e:
            print(f"Warning: Failed to publish call started event: {e}")

        return created_call

    async def end_call(self, call_id: uuid.UUID) -> Call | None:
        """
        Orchestrates ending a call, terminating the media session and updating status.
        """
        db_call = await self.call_repo.get_by_id(call_id)
        if not db_call or db_call.status == CallStatus.ENDED:
            return db_call

        # Delete the media session
        if db_call.media_session_id:
            try:
                await self.voice_router.end_media_session(db_call.media_session_id)
            except Exception as e:
                print(f"Warning: Could not end media session {db_call.media_session_id}: {e}")

        # Calculate call duration
        call_start_time = db_call.created_at
        call_end_time = datetime.now(timezone.utc)
        duration_seconds = (call_end_time - call_start_time).total_seconds()

        # Update call in DB
        db_call.status = CallStatus.ENDED
        db_call.ended_at = call_end_time
        updated_call = await self.call_repo.save(db_call)

        # Publish call ended event
        try:
            event = CallEndedEvent(
                call_id=str(updated_call.id),
                duration_seconds=duration_seconds
            )
            await self.event_publisher.publish_event(event)
        except Exception as e:
            print(f"Warning: Failed to publish call ended event: {e}")

        return updated_call
