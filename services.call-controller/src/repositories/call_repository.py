import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from ..models.call import Call


class CallRepository:
    """
    Handles all database operations for the Call model.
    """
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def get_by_id(self, call_id: uuid.UUID) -> Call | None:
        """Retrieves a call by its primary key."""
        return await self.db_session.get(Call, call_id)

    async def create(self, call: Call) -> Call:
        """Adds a new call to the database."""
        self.db_session.add(call)
        await self.db_session.commit()
        await self.db_session.refresh(call)
        return call

    async def save(self, call: Call) -> Call:
        """Commits changes for an existing call object."""
        await self.db_session.commit()
        await self.db_session.refresh(call)
        return call