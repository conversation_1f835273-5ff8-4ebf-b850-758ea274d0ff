import uuid
import enum
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Enum as SQLAlchemyEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declarative_base

Base = declarative_base()


class CallStatus(str, enum.Enum):
    """Enumeration for the status of a call."""
    QUEUED = "QUEUED"
    ACTIVE = "ACTIVE"
    ENDED = "ENDED"


class Call(Base):
    """
    SQLAlchemy model representing a single call in the system.
    """
    __tablename__ = "calls"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    status = Column(SQLAlchemyEnum(CallStatus), nullable=False, default=CallStatus.QUEUED)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    ended_at = Column(DateTime, nullable=True)
    
    # The unique ID for the session in the voice-router service
    media_session_id = Column(String, nullable=True, unique=True)
    
    # The unique ID for the translation fork in the voice-router service
    translation_fork_id = Column(String, nullable=True)