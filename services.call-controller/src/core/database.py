from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from.config import settings

# Create an async engine to connect to the PostgreSQL database
engine = create_async_engine(settings.database_url, echo=True)

# Create a factory for generating new async sessions
AsyncSessionLocal = sessionmaker(
    bind=engine, class_=AsyncSession, expire_on_commit=False
)
