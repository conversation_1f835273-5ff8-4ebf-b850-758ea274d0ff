from fastapi import FastAPI
from prometheus_fastapi_instrumentator import Instrumentator

from cortexacommon.logging import get_logger

from .config import settings

logger = get_logger(__name__)


class Metrics:
    """Helper class for collecting custom metrics."""

    @staticmethod
    def setup_fastapi_metrics(app: FastAPI) -> None:
        if settings.monitoring.metrics_enabled:
            logger.info("Metrics disabled, skipping initialization")
            return

        # Initialize FastAPI instrumentator
        instrumentator = Instrumentator(
            excluded_handlers=["/metrics"],
            should_group_status_codes=True,
            should_ignore_untemplated=True,
            should_instrument_requests_inprogress=True,
            inprogress_name="http_requests_inprogress",
            inprogress_labels=True,
        )
        instrumentator.instrument(app, metric_subsystem="voice_gateway")

        logger.info("Exposing metrics endpoint at /metrics...")
        instrumentator.expose(app, endpoint="/metrics")

        logger.info("Prometheus metrics initialized successfully")
