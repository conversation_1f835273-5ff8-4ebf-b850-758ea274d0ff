import asyncio
from sqlalchemy.ext.asyncio import Async<PERSON>ession

from cortexacommon.logging import get_logger

from .database import Async<PERSON><PERSON><PERSON><PERSON>ocal
from .events import EventPublisher
from ..repositories.call_repository import CallRepository
from ..services.call_service import CallService
from ..services.voice_router import VoiceRouterService


logger = get_logger(__name__)


class ApplicationContext:
    """
    Application context for managing shared resources.
    
    This class provides a singleton pattern for managing application-wide resources
    """
    
    _instance: 'ApplicationContext | None' = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        """Initialize the application context."""
        self._initialized = False

        self._db_session: AsyncSession | None = None
        self._voice_router_service: VoiceRouterService | None = None
        self._call_service: CallService | None = None
        self._event_publisher: EventPublisher | None = None
       

    @classmethod
    async def get_instance(cls) -> 'ApplicationContext':
        """
        Get the singleton instance of the application context.
        
        Returns:
            ApplicationContext: The singleton instance
        """
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    @property
    def is_initialized(self) -> bool:
        """
        Check if the application context is initialized.
        
        Returns:
            bool: True if initialized, False otherwise
        """
        return self._initialized
    
    async def initialize(self) -> None:
        """
        Initialize the application context and its resources.
        """
        if self._initialized:
            logger.warning("Application context already initialized")
            return

        logger.info("Initializing application context...")
        try:
            self._db_session = AsyncSessionLocal()
            self._voice_router_service = VoiceRouterService()

            # Initialize event publisher
            self._event_publisher = EventPublisher()
            await self._event_publisher.initialize()

            self._call_service = CallService(
                CallRepository(self.db_session),
                self.voice_router_service,
                self.event_publisher
            )

            self._initialized = True
            logger.info("Application context initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize application context: {e}")
            self._initialized = False
            raise

    async def cleanup(self) -> None:
        """
        Clean up the application context and its resources.
        
        This method should be called during application shutdown.
        """
        logger.info("Cleaning up application context...")

        try:
            # Clean up event publisher
            if self._event_publisher:
                await self._event_publisher.cleanup()
                self._event_publisher = None

            # Close the database session
            if self._db_session:
                await self._db_session.close()
                self._db_session = None

            self._initialized = False
            logger.info("Application context cleanup complete")
            
        except Exception as e:
            logger.error(f"Error during application context cleanup: {e}")
    
    @classmethod
    async def reset_instance(cls) -> None:
        """
        Reset the singleton instance (mainly for testing).
        
        This method should only be used in test scenarios.
        """
        async with cls._lock:
            if cls._instance:
                await cls._instance.cleanup()
            cls._instance = None

    @property
    def db_session(self) -> AsyncSession:
        """Get the database session."""
        if not self._db_session:
            raise RuntimeError("Database session not initialized")
        return self._db_session
    
    @property
    def voice_router_service(self) -> VoiceRouterService:
        """Get the voice router service."""
        if not self._voice_router_service:
            raise RuntimeError("Voice router service not initialized")
        return self._voice_router_service
    
    @property
    def call_service(self) -> CallService:
        """Get the call service."""
        if not self._call_service:
            raise RuntimeError("Call service not initialized")
        return self._call_service

    @property
    def event_publisher(self) -> EventPublisher:
        """Get the event publisher."""
        if not self._event_publisher:
            raise RuntimeError("Event publisher not initialized")
        return self._event_publisher


# Global function to get the application context
async def get_app_context() -> ApplicationContext:
    """
    Get the application context instance.
    
    Returns:
        ApplicationContext: The application context instance
    """
    return await ApplicationContext.get_instance()
